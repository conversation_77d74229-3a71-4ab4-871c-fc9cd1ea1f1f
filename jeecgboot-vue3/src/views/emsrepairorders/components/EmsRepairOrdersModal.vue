<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" :width="800" @ok="handleSubmit">
      <BasicForm @register="registerForm" name="EmsRepairOrdersForm" />
      <!-- 临时调试按钮 -->
      <div style="margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 4px;">
        <a-button type="primary" size="small" @click="debugApprovalConfig">调试审批配置</a-button>
        <a-button type="danger" size="small" style="margin-left: 10px;" @click="forceSubmit">强制提交（跳过验证）</a-button>
        <span style="margin-left: 10px; font-size: 12px; color: #666;">临时调试工具</span>
      </div>
  </BasicModal>
</template>

<script lang="ts" setup>
    import {ref, computed, unref, reactive} from 'vue';
    import {BasicModal, useModalInner} from '/@/components/Modal';
    import {BasicForm, useForm} from '/@/components/Form/index';
    import {formSchema} from '../EmsRepairOrders.data';
    import {saveOrUpdate, createRepairOrder, checkActiveTemplate} from '../EmsRepairOrders.api';
    import { useMessage } from '/@/hooks/web/useMessage';
    import { getDateByPicker } from '/@/utils';
    import { useUserStore } from '/@/store/modules/user';
    const { createMessage } = useMessage();
    const userStore = useUserStore();
    // Emits声明
    const emit = defineEmits(['register','success']);
    const isUpdate = ref(true);
    const isDetail = ref(false);
    const approvalStepsData = ref([]);
    const currentApprovalConfig = ref([]);
    //表单配置
    const [registerForm, { setProps,resetFields, setFieldsValue, validate, scrollToField, getFieldsValue }] = useForm({
        labelWidth: 150,
        schemas: formSchema,
        showActionButtonGroup: false,
        baseColProps: {span: 24}
    });
    //表单赋值
    const [registerModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
        //重置表单
        await resetFields();
        setModalProps({confirmLoading: false,showCancelBtn:!!data?.showFooter,showOkBtn:!!data?.showFooter});
        isUpdate.value = !!data?.isUpdate;
        isDetail.value = !!data?.showFooter;
        if (unref(isUpdate)) {
            //表单赋值
            await setFieldsValue({
                ...data.record,
            });
        } else {
            // 新增时设置默认值并获取当前审批模板
            try {
                const templateResult = await checkActiveTemplate();
                console.log('获取到的审批模板:', templateResult);



                // 保存审批步骤数据并显示审批人员配置字段
                if (templateResult.selectedRoles && templateResult.selectedRoles.length > 0) {
                    approvalStepsData.value = templateResult.selectedRoles;

                    // 为每个步骤初始化selectedUsers字段
                    const stepsWithUsers = templateResult.selectedRoles.map(step => ({
                        ...step,
                        selectedUsers: step.selectedUsers || [],
                        selectedUser: step.selectedUser || null
                    }));

                    setProps({
                        schemas: formSchema.map(schema => {
                            if (schema.field === 'approvalUserConfig') {
                                return {
                                    ...schema,
                                    show: true,
                                    componentProps: {
                                        ...schema.componentProps,
                                        approvalSteps: stepsWithUsers,
                                        onChange: (stepData) => {
                                            console.log('审批配置变化:', stepData);
                                            // 保存到本地变量
                                            currentApprovalConfig.value = stepData;
                                            // 手动更新表单字段值
                                            setFieldsValue({
                                                approvalUserConfig: stepData
                                            });
                                        }
                                    }
                                };
                            }
                            return schema;
                        })
                    });

                    // 设置初始值
                    await setFieldsValue({
                        currentStatus: '1', // 默认状态为审核中
                        reportId: userStore.getUserInfo?.id || userStore.getUserInfo?.username, // 设置当前用户为发起人
                        approvalUserConfig: stepsWithUsers, // 设置审批配置初始值
                    });
                } else {
                    createMessage.warning('当前部门没有激活的审批模板，请联系管理员配置');
                }

            } catch (error) {
                console.error('获取审批模板失败:', error);
                createMessage.error('获取审批模板失败: ' + (error.message || '未知错误'));
                await setFieldsValue({
                    currentStatus: '1',
                    reportId: userStore.getUserInfo?.id || userStore.getUserInfo?.username, // 设置当前用户为发起人
                    approvalUserConfig: [], // 初始化审批配置为空数组
                });
            }
        }
        // 隐藏底部时禁用整个表单
       setProps({ disabled: !data?.showFooter })
    });
    //日期个性化选择
    const fieldPickers = reactive({
    });
    //设置标题
    const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(isDetail) ? '详情' : '编辑'));
    //表单提交事件
    async function handleSubmit(v) {
        try {
            let values = await validate();
            // 预处理日期数据
            changeDateValue(values);
            setModalProps({confirmLoading: true});

            if (unref(isUpdate)) {
                // 编辑模式：使用原有的saveOrUpdate方法
                await saveOrUpdate(values, true);
            } else {
                // 新增模式：构造DTO格式并调用专门的创建接口
                console.log('表单原始数据:', values);
                console.log('审批配置数据:', values.approvalUserConfig);
                console.log('审批步骤模板数据:', approvalStepsData.value);
                console.log('本地保存的审批配置:', currentApprovalConfig.value);

                // 获取当前表单所有值进行调试
                const currentFormValues = getFieldsValue();
                console.log('当前表单所有值:', currentFormValues);

                // 处理审批用户配置数据 - 简化逻辑
                let finalApprovalConfig = [];

                console.log('=== 开始处理审批配置数据 ===');
                console.log('1. 表单值中的审批配置:', values.approvalUserConfig);
                console.log('2. 本地保存的审批配置:', currentApprovalConfig.value);
                console.log('3. 模板审批配置:', approvalStepsData.value);

                // 优先使用本地保存的配置（最新的用户选择）
                if (currentApprovalConfig.value && currentApprovalConfig.value.length > 0) {
                    finalApprovalConfig = currentApprovalConfig.value;
                    console.log('使用本地保存的配置');
                }
                // 其次使用表单值
                else if (values.approvalUserConfig && Array.isArray(values.approvalUserConfig)) {
                    finalApprovalConfig = values.approvalUserConfig;
                    console.log('使用表单值配置');
                }
                // 最后使用模板数据
                else if (approvalStepsData.value && approvalStepsData.value.length > 0) {
                    finalApprovalConfig = approvalStepsData.value;
                    console.log('使用模板配置');
                }

                console.log('选择的配置源:', finalApprovalConfig);

                // 过滤出有效的审批配置（已选择用户的步骤）
                const validApprovalConfig = finalApprovalConfig.filter(step => {
                    const hasUsers = step.selectedUsers && step.selectedUsers.length > 0;
                    const hasUser = step.selectedUser && step.selectedUser.trim() !== '';
                    console.log(`步骤${step.sortOrder} - 角色:${step.roleName}, 有用户列表:${hasUsers}, 有单个用户:${hasUser}`);
                    return hasUsers || hasUser;
                }).map(step => ({
                    roleId: step.roleId,
                    roleCode: step.roleCode,
                    roleName: step.roleName,
                    sortOrder: step.sortOrder,
                    selectedUsers: step.selectedUsers && step.selectedUsers.length > 0
                        ? step.selectedUsers
                        : (step.selectedUser ? [step.selectedUser] : [])
                }));

                console.log('有效的审批配置:', validApprovalConfig);

                // 如果没有有效的审批配置，提示用户
                if (validApprovalConfig.length === 0) {
                    // 尝试更宽松的验证 - 检查是否有任何审批步骤数据
                    const hasAnySteps = finalApprovalConfig.length > 0;
                    if (hasAnySteps) {
                        console.log('检测到审批步骤但没有选择用户，尝试使用默认配置');
                        // 如果有步骤但没有用户，给出更具体的提示
                        createMessage.warning('请为每个审批步骤选择具体的审批人员');
                    } else {
                        console.log('没有检测到任何审批步骤');
                        createMessage.warning('请为审批步骤选择审批人员');
                    }
                    console.log('=== 审批配置验证失败 ===');
                    return;
                }

                // 使用有效的配置
                finalApprovalConfig = validApprovalConfig;

                const createDTO = {
                    repairOrder: {
                        equipmentId: values.equipmentId,
                        reportId: values.reportId || userStore.getUserInfo?.id || userStore.getUserInfo?.username,
                        principalId: values.principalId,
                        faultTitle: values.faultTitle,
                        faultDescription: values.faultDescription,
                        attachment: values.attachment,
                        faultAttachment: values.faultAttachment,
                        handleImages: values.handleImages,
                        handleAttachment: values.handleAttachment,
                        // currentStatus 由后端自动设置，不需要传递
                    },
                    approvalUserConfig: finalApprovalConfig
                };

                console.log('提交的创建DTO:', createDTO);
                console.log('当前用户信息:', userStore.getUserInfo);
                await createRepairOrder(createDTO);
            }

            //关闭弹窗
            closeModal();
            //刷新列表
            emit('success');
        } catch ({ errorFields }) {
           if (errorFields) {
             const firstField = errorFields[0];
             if (firstField) {
               scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
             }
           }
           return Promise.reject(errorFields);
        } finally {
            setModalProps({confirmLoading: false});
        }
    }

    /**
     * 处理日期值
     * @param formData 表单数据
     */
    const changeDateValue = (formData) => {
        if (formData && fieldPickers) {
            for (let key in fieldPickers) {
                if (formData[key]) {
                    formData[key] = getDateByPicker(formData[key], fieldPickers[key]);
                }
            }
        }
    };

    /**
     * 调试审批配置
     */
    const debugApprovalConfig = () => {
        console.log('=== 调试审批配置 ===');
        const formValues = getFieldsValue();
        console.log('当前表单值:', formValues);
        console.log('本地保存的审批配置:', currentApprovalConfig.value);
        console.log('模板审批配置:', approvalStepsData.value);

        // 检查每个数据源的详细信息
        if (formValues.approvalUserConfig) {
            console.log('表单审批配置详情:', JSON.stringify(formValues.approvalUserConfig, null, 2));
        }
        if (currentApprovalConfig.value) {
            console.log('本地审批配置详情:', JSON.stringify(currentApprovalConfig.value, null, 2));
        }
        if (approvalStepsData.value) {
            console.log('模板审批配置详情:', JSON.stringify(approvalStepsData.value, null, 2));
        }

        createMessage.info('调试信息已输出到控制台，请查看');
    };

    /**
     * 强制提交（跳过审批配置验证）
     */
    const forceSubmit = async () => {
        try {
            let values = await validate();
            changeDateValue(values);
            setModalProps({confirmLoading: true});

            console.log('=== 强制提交模式 ===');

            // 构造基本的DTO，使用空的审批配置
            const createDTO = {
                repairOrder: {
                    equipmentId: values.equipmentId,
                    reportId: values.reportId || userStore.getUserInfo?.id || userStore.getUserInfo?.username,
                    principalId: values.principalId,
                    faultTitle: values.faultTitle,
                    faultDescription: values.faultDescription,
                    attachment: values.attachment,
                    faultAttachment: values.faultAttachment,
                    handleImages: values.handleImages,
                    handleAttachment: values.handleAttachment,
                },
                approvalUserConfig: [] // 空的审批配置
            };

            console.log('强制提交的DTO:', createDTO);
            await createRepairOrder(createDTO);

            closeModal();
            emit('success');
        } catch (error) {
            console.error('强制提交失败:', error);
            createMessage.error('强制提交失败: ' + error.message);
        } finally {
            setModalProps({confirmLoading: false});
        }
    };

</script>

<style lang="less" scoped>
	/** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
