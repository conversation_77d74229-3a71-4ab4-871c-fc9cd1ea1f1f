import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '关联设备ID',
    align:"center",
    dataIndex: 'equipmentId'
   },
   {
    title: '发起人用户ID',
    align:"center",
    dataIndex: 'reportId_dictText'
   },
   {
    title: '处理负责人用户ID',
    align:"center",
    dataIndex: 'principalId_dictText'
   },
   {
    title: '故障标题',
    align:"center",
    dataIndex: 'faultTitle'
   },
   {
    title: '故障描述',
    align:"center",
    dataIndex: 'faultDescription'
   },
   {
    title: '故障图片',
    align:"center",
    dataIndex: 'attachment',
    customRender:render.renderImage,
   },
   {
    title: '故障附件',
    align:"center",
    dataIndex: 'faultAttachment',
   },
   {
    title: '工单状态：1=审核中 2=驳回 3=重新提交 4=处理中 5=已完成 6=已关闭',
    align:"center",
    dataIndex: 'currentStatus'
   },
   {
    title: '处理图片',
    align:"center",
    dataIndex: 'handleImages',
    customRender:render.renderImage,
   },
   {
    title: '处理附件',
    align:"center",
    dataIndex: 'handleAttachment',
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '关联设备ID',
    field: 'equipmentId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入关联设备ID!'},
          ];
     },
  },
  {
    label: '发起人用户ID',
    field: 'reportId',
    component: 'JSelectUser',
    componentProps:{
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入发起人用户ID!'},
          ];
     },
  },
  {
    label: '处理负责人用户ID',
    field: 'principalId',
    component: 'JSelectUser',
    componentProps:{
    },
    show: false
  },
  {
    label: '故障标题',
    field: 'faultTitle',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入故障标题!'},
          ];
     },
  },
  {
    label: '故障描述',
    field: 'faultDescription',
    component: 'InputTextArea',
  },
  {
    label: '故障图片',
    field: 'attachment',
     component: 'JImageUpload',
     componentProps:{
        fileMax: 0
      },
  },
  {
    label: '故障附件',
    field: 'faultAttachment',
    component: 'JUpload',
    componentProps:{
     },
  },
  {
    label: '工单状态',
    field: 'currentStatus',
    component: 'Input',
    show: false,
    // 移除必填验证，因为状态由后端自动设置
  },
  {
    label: '处理图片',
    field: 'handleImages',
     component: 'JImageUpload',
     componentProps:{
        fileMax: 0
      },
    show: false
  },
  {
    label: '处理附件',
    field: 'handleAttachment',
    component: 'JUpload',
    componentProps:{
     },
    show: false
  },

  {
    label: '审批人员配置',
    field: 'approvalUserConfig',
    component: 'JApprovalStepUserSelector',
    componentProps: {
      placeholder: '请为每个审批步骤选择具体的审批人员',
    },
    show: false
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  equipmentId: {title: '关联设备ID',order: 0,view: 'text', type: 'string',},
  reportId: {title: '发起人用户ID',order: 1,view: 'sel_user', type: 'string',},
  principalId: {title: '处理负责人用户ID',order: 2,view: 'sel_user', type: 'string',},
  faultTitle: {title: '故障标题',order: 3,view: 'text', type: 'string',},
  faultDescription: {title: '故障描述',order: 4,view: 'textarea', type: 'string',},
  attachment: {title: '故障图片',order: 5,view: 'image', type: 'string',},
  faultAttachment: {title: '故障附件',order: 6,view: 'file', type: 'string',},
  currentStatus: {title: '工单状态：1=审核中 2=驳回 3=重新提交 4=处理中 5=已完成 6=已关闭',order: 7,view: 'text', type: 'string',},
  handleImages: {title: '处理图片',order: 8,view: 'image', type: 'string',},
  handleAttachment: {title: '处理附件',order: 9,view: 'file', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
